package com.fasnote.alm.plugin.manage.injection.module;

import org.osgi.framework.BundleContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasnote.alm.injection.api.IBinder;
import com.fasnote.alm.injection.api.IInjectionContext;
import com.fasnote.alm.injection.api.IModule;
import com.fasnote.alm.injection.api.IServiceProvider;
import com.fasnote.alm.injection.facade.DI;
import com.fasnote.alm.plugin.manage.Activator;
import com.fasnote.alm.plugin.manage.api.LicenseAware;
import com.fasnote.alm.plugin.manage.core.LicenseManager;
import com.fasnote.alm.plugin.manage.injection.interceptor.LicenseAwareServiceInterceptor;
import com.fasnote.alm.plugin.manage.model.PluginLicense;

/**
 * 统一许可证模块
 *
 * 整合了 LicenseManager 的依赖注入配置和许可证感知服务功能
 *
 * 核心功能：
 * 1. 配置 LicenseManager 的依赖注入（单例模式）
 * 2. 注册许可证感知服务拦截器
 * 3. 根据许可证状态选择 LicenseImplementation 或 FallbackImplementation
 * 4. 支持许可证动态更新时的服务切换
 */
public class LicenseModule implements IModule {

	/**
	 * LicenseManager 服务提供者
	 */
	private class LicenseManagerProvider implements IServiceProvider<LicenseManager> {

		@Override
		public LicenseManager provide(IInjectionContext context) {
			logger.debug("创建LicenseManager实例...");

			try {
				// 确保有可用的 BundleContext
				BundleContext contextToUse = Activator.getContext();

				LicenseManager licenseManager = new LicenseManager(contextToUse);
				logger.info("LicenseManager实例创建成功");
				return licenseManager;
			} catch (Exception e) {
				logger.error("创建LicenseManager实例失败", e);
				throw new RuntimeException("无法创建LicenseManager实例", e);
			}
		}
	}

	/**
	 * 许可证感知服务提供者
	 */
	private class LicenseAwareProvider implements IServiceProvider<LicenseAware> {

		@Override
		public LicenseAware provide(IInjectionContext context) {
			// 返回一个简单的许可证信息访问器
			return new LicenseAware() {
				private PluginLicense license;

				@Override
				public PluginLicense getLicenseInfo() {
					return license;
				}

				@Override
				public void setLicenseInfo(PluginLicense license) {
					this.license = license;
				}
			};
		}
	}

	private static final Logger logger = LoggerFactory.getLogger(LicenseModule.class);
	private LicenseManager licenseManager;
	private LicenseAwareServiceInterceptor licenseAwareInterceptor;

	/**
	 * 无参构造函数（用于依赖注入框架自动扫描）
	 *
	 * 模块注册的设计原则：
	 * 1. 模块应该是配置单元，不应该依赖外部状态
	 * 2. 模块的依赖应该通过 configure() 方法中的依赖查找获取
	 * 3. 这样可以确保模块的可重用性和测试性
	 */
	public LicenseModule() {
		logger.info("LicenseModule使用无参构造函数创建");
	}

	@Override
	public void configure(IBinder binder) {
		logger.info("配置统一许可证模块...");

		// 首先注册 LicenseManager 为单例服务
		logger.info("开始配置LicenseManager依赖注入...");
		binder.bind(LicenseManager.class).toProvider(new LicenseManagerProvider()).asSingleton().build();
		logger.info("LicenseManager依赖注入配置完成");

		// 获取刚注册的 LicenseManager 实例
		this.licenseManager = DI.get(LicenseManager.class);
		if (this.licenseManager == null) {
			throw new IllegalStateException("无法获取 LicenseManager 实例");
		}
		logger.debug("成功获取 LicenseManager 实例");

		// 注册许可证感知服务拦截器
		this.licenseAwareInterceptor = new LicenseAwareServiceInterceptor(licenseManager);
		binder.registerInterceptor(licenseAwareInterceptor);
		logger.info("许可证感知服务拦截器已注册，优先级: {}", licenseAwareInterceptor.getPriority());

		// 注册许可证感知服务提供者
		binder.bind(LicenseAware.class).toProvider(new LicenseAwareProvider()).build();

		logger.info("统一许可证模块配置完成");
	}

	/**
	 * 清理实现类缓存（支持许可证动态更新）
	 *
	 * @param serviceInterface 要清理的服务接口，如果为null则清理所有缓存
	 */
	public void clearImplementationCache(Class<?> serviceInterface) {
		if (licenseAwareInterceptor != null) {
			licenseAwareInterceptor.clearImplementationCache(serviceInterface);
			logger.info("已清理许可证实现缓存: {}", serviceInterface != null ? serviceInterface.getName() : "全部");
		}
	}

	@Override
	public String getName() {
		return "UnifiedLicenseModule";
	}

	@Override
	public int getPriority() {
		return 5; // 更高优先级，确保 LicenseManager 在其他许可证相关模块之前初始化
	}







	/**
	 * 基于接口名称提供服务 许可证验证系统的核心方法，支持动态服务查找
	 *
	 * @param interfaceName 接口全限定名称
	 * @param context       注入上下文
	 * @return 服务实例，如果未找到返回null
	 */
	public Object provideServiceByInterfaceName(String interfaceName, IInjectionContext context) {
		if (licenseManager == null) {
			logger.debug("LicenseManager未初始化");
			return null;
		}

		logger.debug("基于接口名称查找许可证服务: {}", interfaceName);

		try {
			// 尝试通过接口名称加载接口类
			Class<?> interfaceClass = Class.forName(interfaceName);

			// 使用LicenseManager创建服务实例
			Object instance = licenseManager.createServiceInstanceFromLicense(interfaceClass);
			if (instance != null) {
				logger.info("成功通过接口名称创建服务实例: {}", interfaceName);
				return instance;
			}
		} catch (ClassNotFoundException e) {
			logger.warn("无法加载接口类: {}", interfaceName, e);
		} catch (Exception e) {
			logger.error("通过接口名称创建服务实例失败: {}", interfaceName, e);
		}

		logger.debug("未找到匹配的许可证服务: {}", interfaceName);
		return null;
	}

}